import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const search = url.searchParams.get('search') || '';
    const limit = parseInt(url.searchParams.get('limit') || '100');

    // Check if prisma client is available
    if (!prisma) {
      console.error('Prisma client is not initialized');
      return json({ error: 'Database connection not available' }, { status: 500 });
    }

    // Get companies from the database
    const companies = await prisma.company.findMany({
      where: {
        name: { contains: search, mode: 'insensitive' },
      },
      select: {
        id: true,
        name: true,
        domain: true,
        logoUrl: true,
      },
      take: limit,
      orderBy: {
        name: 'asc',
      },
    });

    return json(companies);
  } catch (error) {
    console.error('Error fetching companies:', error);
    return json({ error: 'Failed to fetch companies' }, { status: 500 });
  }
};
