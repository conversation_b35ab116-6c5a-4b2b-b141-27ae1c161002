<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Label } from '$lib/components/ui/label';
  import { Input } from '$lib/components/ui/input';
  import { Textarea } from '$lib/components/ui/textarea';
  import * as Select from '$lib/components/ui/select';
  import { Loader2, Plus } from 'lucide-svelte';
  import MultiCombobox from '$lib/components/ui/combobox/multi-combobox.svelte';
  import { onMount } from 'svelte';

  // Props
  let {
    open = $bindable(),
    form,
    enhance,
    reset,
    errors,
    constraints,
    submitting,
    jobTypes,
    jobStatuses,
  } = $props();

  // State for location, documents, and positions
  let locationOptions = $state([]);
  let documentOptions = $state([]);
  let selectedLocation = $state([]);
  let selectedPosition = $state([]);
  let selectedDocument = $state('');

  // Load location and document options
  onMount(async () => {
    await Promise.all([loadLocationOptions(), loadDocumentOptions()]);
  });

  // Load location options from API
  async function loadLocationOptions() {
    try {
      const response = await fetch('/api/locations/search?limit=100');
      if (response.ok) {
        const locations = await response.json();
        locationOptions = locations.map((loc: any) => ({
          value: `${loc.id}|${loc.name}|${loc.state?.code || ''}|${loc.country || 'US'}`,
          label: `${loc.name}, ${loc.state?.code || loc.country || 'US'}`,
        }));
      }
    } catch (error) {
      console.error('Error loading locations:', error);
    }
  }

  // Load user's documents/resumes
  async function loadDocumentOptions() {
    try {
      const response = await fetch('/api/documents?type=resume');
      if (response.ok) {
        const data = await response.json();
        const documents = data.documents || data;
        documentOptions = [
          { value: 'Yes', label: 'Yes - Resume uploaded' },
          { value: 'No', label: 'No - No resume uploaded' },
          { value: 'N/A', label: 'N/A - Not applicable' },
          ...documents.map((doc: any) => ({
            value: doc.id,
            label: `${doc.label || doc.fileName || 'Unnamed Resume'}`,
          })),
        ];
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      // Fallback to basic options
      documentOptions = [
        { value: 'Yes', label: 'Yes - Resume uploaded' },
        { value: 'No', label: 'No - resume uploaded' },
        { value: 'N/A', label: 'N/A - Not applicable' },
      ];
    }
  }

  // Search occupations function
  async function searchOccupations(query: string): Promise<{ value: string; label: string }[]> {
    if (!query || query.length < 2) {
      return [];
    }

    try {
      const response = await fetch(`/api/occupations?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const occupations = await response.json();
        const results = occupations.map((occupation: any) => ({
          value: occupation.title,
          label: occupation.title,
        }));

        // If no results found, allow custom value
        if (results.length === 0) {
          return [{ value: query, label: `Add "${query}" as custom position` }];
        }

        return results;
      }
    } catch (error) {
      console.error('Error searching occupations:', error);
    }

    // Fallback: allow custom value
    return [{ value: query, label: `Add "${query}" as custom position` }];
  }

  // Handle location selection
  function handleLocationChange(values: string[]) {
    selectedLocation = values;
    // Set the form location to the first selected location's display name
    if (values.length > 0) {
      const locationData = values[0].split('|');
      if (locationData.length >= 3) {
        $form.location = `${locationData[1]}, ${locationData[2]}`;
      } else {
        $form.location = values[0];
      }
    } else {
      $form.location = '';
    }
  }

  // Handle position selection
  function handlePositionChange(values: string[]) {
    selectedPosition = values;
    $form.position = values.join(', ');
  }

  // Reset form when modal is closed
  $effect(() => {
    if (!open) {
      reset();
      selectedLocation = [];
      selectedPosition = [];
      selectedDocument = '';
    }
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="flex max-h-[90vh] flex-col overflow-hidden sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Add New Job Application</Dialog.Title>
      <Dialog.Description>Enter the details of your new job application.</Dialog.Description>
    </Dialog.Header>

    <div class="flex-1 overflow-y-auto">
      <form method="POST" action="?/addJob" use:enhance>
        <div class="grid grid-cols-1 gap-4 py-4 md:grid-cols-2">
          <!-- Company -->
          <div class="space-y-2">
            <Label for="company">Company *</Label>
            <Input
              id="company"
              name="company"
              bind:value={$form.company}
              placeholder="Enter company name"
              {...$constraints.company} />
            {#if $errors.company}
              <p class="text-sm text-red-500">{$errors.company}</p>
            {/if}
          </div>

          <!-- Position -->
          <div class="space-y-2">
            <Label for="position">Position *</Label>
            <MultiCombobox
              options={[]}
              selectedValues={selectedPosition}
              placeholder="Search for positions..."
              searchPlaceholder="Search occupations..."
              emptyMessage="No positions found"
              width="w-full"
              maxDisplayItems={1}
              searchOptions={searchOccupations}
              onSelectedValuesChange={handlePositionChange} />
            <input type="hidden" name="position" bind:value={$form.position} />
            {#if $errors.position}
              <p class="text-sm text-red-500">{$errors.position}</p>
            {/if}
          </div>

          <!-- Location -->
          <div class="space-y-2">
            <Label for="location">Location</Label>
            <MultiCombobox
              options={locationOptions}
              selectedValues={selectedLocation}
              placeholder="Search for cities..."
              searchPlaceholder="Search locations..."
              emptyMessage="No locations found"
              width="w-full"
              maxDisplayItems={1}
              onSelectedValuesChange={handleLocationChange} />
            <input type="hidden" name="location" bind:value={$form.location} />
            {#if $errors.location}
              <p class="text-sm text-red-500">{$errors.location}</p>
            {/if}
          </div>

          <!-- Job Type -->
          <div class="space-y-2">
            <Label for="jobType">Job Type *</Label>
            <Select.Root
              type="single"
              value={$form.jobType}
              onValueChange={(value) => {
                $form.jobType = value || '';
              }}>
              <Select.Trigger id="jobType" class="w-full">
                <Select.Value placeholder="Select job type" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each jobTypes as type}
                    <Select.Item value={type}>{type}</Select.Item>
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="jobType" bind:value={$form.jobType} />
            {#if $errors.jobType}
              <p class="text-sm text-red-500">{$errors.jobType}</p>
            {/if}
          </div>

          <!-- Applied Date -->
          <div class="space-y-2">
            <Label for="appliedDate">Applied Date *</Label>
            <Input
              id="appliedDate"
              name="appliedDate"
              type="date"
              bind:value={$form.appliedDate}
              {...$constraints.appliedDate} />
            {#if $errors.appliedDate}
              <p class="text-sm text-red-500">{$errors.appliedDate}</p>
            {/if}
          </div>

          <!-- Status -->
          <div class="space-y-2">
            <Label for="status">Status *</Label>
            <Select.Root
              type="single"
              value={$form.status}
              onValueChange={(value) => {
                $form.status = value || '';
              }}>
              <Select.Trigger id="status" class="w-full">
                <Select.Value placeholder="Select status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each jobStatuses as status}
                    <Select.Item value={status}>{status}</Select.Item>
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="status" bind:value={$form.status} />
            {#if $errors.status}
              <p class="text-sm text-red-500">{$errors.status}</p>
            {/if}
          </div>

          <!-- Resume -->
          <div class="space-y-2">
            <Label for="resumeUploaded">Resume *</Label>
            <Select.Root
              type="single"
              value={$form.resumeUploaded}
              onValueChange={(value) => {
                $form.resumeUploaded = value || '';
                selectedDocument = value || '';
              }}>
              <Select.Trigger id="resumeUploaded" class="w-full">
                <Select.Value placeholder="Select resume or status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each documentOptions as option}
                    <Select.Item value={option.value}>{option.label}</Select.Item>
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="resumeUploaded" bind:value={$form.resumeUploaded} />
            {#if $errors.resumeUploaded}
              <p class="text-sm text-red-500">{$errors.resumeUploaded}</p>
            {/if}
          </div>

          <!-- URL -->
          <div class="space-y-2">
            <Label for="url">Job URL</Label>
            <Input
              id="url"
              name="url"
              bind:value={$form.url}
              placeholder="https://example.com/job/123"
              {...$constraints.url} />
            {#if $errors.url}
              <p class="text-sm text-red-500">{$errors.url}</p>
            {/if}
          </div>

          <!-- Next Action -->
          <div class="space-y-2">
            <Label for="nextAction">Next Action</Label>
            <Input
              id="nextAction"
              name="nextAction"
              bind:value={$form.nextAction}
              placeholder="Follow up with recruiter"
              {...$constraints.nextAction} />
            {#if $errors.nextAction}
              <p class="text-sm text-red-500">{$errors.nextAction}</p>
            {/if}
          </div>

          <!-- Notes - Full width -->
          <div class="space-y-2 md:col-span-2">
            <Label for="notes">Notes</Label>
            <Textarea
              id="notes"
              name="notes"
              bind:value={$form.notes}
              placeholder="Any additional notes about this application"
              {...$constraints.notes} />
            {#if $errors.notes}
              <p class="text-sm text-red-500">{$errors.notes}</p>
            {/if}
          </div>
        </div>

        <div class="mt-4 flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onclick={() => {
              reset();
              open = false;
            }}>Cancel</Button>
          <Button
            type="submit"
            disabled={submitting}
            class="bg-primary text-primary-foreground hover:bg-primary/90">
            {#if submitting}
              <Loader2 class="mr-2 h-4 w-4 animate-spin" />
              Saving...
            {:else}
              <Plus class="mr-2 h-4 w-4" />
              Add Application
            {/if}
          </Button>
        </div>
      </form>
    </div></Dialog.Content>
</Dialog.Root>
