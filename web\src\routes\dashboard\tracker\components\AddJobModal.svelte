<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Label } from '$lib/components/ui/label';
  import { Input } from '$lib/components/ui/input';
  import { Textarea } from '$lib/components/ui/textarea';
  import * as Select from '$lib/components/ui/select';
  import { Loader2, Plus } from 'lucide-svelte';
  import MultiCombobox from '$lib/components/ui/combobox/multi-combobox.svelte';
  import { onMount } from 'svelte';

  // Props
  let {
    open = $bindable(),
    form,
    enhance,
    reset,
    errors,
    constraints,
    submitting,
    jobTypes,
    jobStatuses,
  } = $props();

  // State for options and selections
  let locationOptions = $state([]);
  let documentOptions = $state([]);
  let occupationOptions = $state([]);
  let companyOptions = $state([]);

  let selectedLocation = $state([]);
  let selectedPosition = $state([]);
  let selectedCompany = $state([]);
  let selectedDocument = $state('');

  // Load initial data
  onMount(async () => {
    await Promise.all([
      loadLocationOptions(),
      loadDocumentOptions(),
      loadOccupationOptions(),
      loadCompanyOptions(),
    ]);
  });

  // Load location options from API
  async function loadLocationOptions() {
    try {
      const response = await fetch('/api/locations/search?limit=100');
      if (response.ok) {
        const locations = await response.json();
        locationOptions = locations.map((loc: any) => ({
          value: `${loc.id}|${loc.name}|${loc.state?.code || ''}|${loc.country || 'US'}`,
          label: `${loc.name}, ${loc.state?.code || loc.country || 'US'}`,
        }));
      }
    } catch (error) {
      console.error('Error loading locations:', error);
    }
  }

  // Load user's documents/resumes
  async function loadDocumentOptions() {
    try {
      const response = await fetch('/api/documents?type=resume');
      if (response.ok) {
        const data = await response.json();
        const documents = data.documents || data;
        documentOptions = [
          { value: 'Yes', label: 'Yes - Resume uploaded' },
          { value: 'No', label: 'No - No resume uploaded' },
          { value: 'N/A', label: 'N/A - Not applicable' },
        ];

        // Add separator and documents if any exist
        if (documents.length > 0) {
          documentOptions.push(
            { value: 'separator', label: '--- Your Resumes ---' },
            ...documents.map((doc: any) => ({
              value: doc.id,
              label: `${doc.label || doc.fileName || 'Unnamed Resume'}`,
            }))
          );
        }
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      // Fallback to basic options
      documentOptions = [
        { value: 'Yes', label: 'Yes - Resume uploaded' },
        { value: 'No', label: 'No - resume uploaded' },
        { value: 'N/A', label: 'N/A - Not applicable' },
      ];
    }
  }

  // Load initial occupation options
  async function loadOccupationOptions() {
    try {
      const response = await fetch('/api/occupations?limit=100');
      if (response.ok) {
        const occupations = await response.json();
        occupationOptions = occupations.map((occupation: any) => ({
          value: occupation.title,
          label: occupation.title,
        }));
      }
    } catch (error) {
      console.error('Error loading occupations:', error);
      occupationOptions = [];
    }
  }

  // Load initial company options
  async function loadCompanyOptions() {
    try {
      const response = await fetch('/api/companies?limit=100');
      if (response.ok) {
        const companies = await response.json();
        companyOptions = companies.map((company: any) => ({
          value: company.name,
          label: company.name,
        }));
      }
    } catch (error) {
      console.error('Error loading companies:', error);
      companyOptions = [];
    }
  }

  // Search occupations function
  async function searchOccupations(query: string): Promise<{ value: string; label: string }[]> {
    if (!query || query.length < 2) {
      return occupationOptions;
    }

    try {
      const response = await fetch(`/api/occupations?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const occupations = await response.json();
        const results = occupations.map((occupation: any) => ({
          value: occupation.title,
          label: occupation.title,
        }));

        // If no results found, allow custom value
        if (results.length === 0) {
          return [{ value: query, label: `Add "${query}" as custom position` }];
        }

        return results;
      }
    } catch (error) {
      console.error('Error searching occupations:', error);
    }

    // Fallback: allow custom value
    return [{ value: query, label: `Add "${query}" as custom position` }];
  }

  // Search companies function
  async function searchCompanies(query: string): Promise<{ value: string; label: string }[]> {
    if (!query || query.length < 2) {
      return companyOptions;
    }

    try {
      const response = await fetch(`/api/companies?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const companies = await response.json();
        const results = companies.map((company: any) => ({
          value: company.name,
          label: company.name,
        }));

        // If no results found, allow custom value
        if (results.length === 0) {
          return [{ value: query, label: `Add "${query}" as custom company` }];
        }

        return results;
      }
    } catch (error) {
      console.error('Error searching companies:', error);
    }

    // Fallback: allow custom value
    return [{ value: query, label: `Add "${query}" as custom company` }];
  }

  // Search locations function
  async function searchLocations(query: string): Promise<{ value: string; label: string }[]> {
    if (!query || query.length < 2) {
      return locationOptions;
    }

    try {
      const response = await fetch(`/api/locations?search=${encodeURIComponent(query)}&limit=20`);
      if (response.ok) {
        const locations = await response.json();
        return locations.map((loc: any) => ({
          value: `${loc.id}|${loc.name}|${loc.state?.code || ''}|${loc.country || 'US'}`,
          label: `${loc.name}, ${loc.state?.code || loc.country || 'US'}`,
        }));
      }
    } catch (error) {
      console.error('Error searching locations:', error);
    }

    return locationOptions;
  }

  // Handle location selection
  function handleLocationChange(values: string[]) {
    selectedLocation = values;
    // Set the form location to the first selected location's display name
    if (values.length > 0) {
      const locationData = values[0].split('|');
      if (locationData.length >= 3) {
        $form.location = `${locationData[1]}, ${locationData[2]}`;
      } else {
        $form.location = values[0];
      }
    } else {
      $form.location = '';
    }
  }

  // Handle position selection
  function handlePositionChange(values: string[]) {
    selectedPosition = values;
    $form.position = values.join(', ');
  }

  // Handle company selection
  function handleCompanyChange(values: string[]) {
    selectedCompany = values;
    $form.company = values.join(', ');
  }

  // Reset form when modal is closed
  $effect(() => {
    if (!open) {
      reset();
      selectedLocation = [];
      selectedPosition = [];
      selectedCompany = [];
      selectedDocument = '';
    }
  });
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="flex max-h-[90vh] flex-col overflow-hidden sm:max-w-[600px]">
    <Dialog.Header>
      <Dialog.Title>Add New Job Application</Dialog.Title>
      <Dialog.Description>Enter the details of your new job application.</Dialog.Description>
    </Dialog.Header>

    <div class="flex-1 overflow-y-auto">
      <form method="POST" action="?/addJob" use:enhance>
        <div class="grid grid-cols-1 gap-4 py-4 md:grid-cols-2">
          <!-- Company -->
          <div class="space-y-2">
            <Label for="company">Company *</Label>
            <MultiCombobox
              options={companyOptions}
              selectedValues={selectedCompany}
              placeholder="Search for companies..."
              searchPlaceholder="Search companies..."
              emptyMessage="No companies found"
              width="w-full"
              maxDisplayItems={1}
              searchOptions={searchCompanies}
              onSelectedValuesChange={handleCompanyChange} />
            <input type="hidden" name="company" bind:value={$form.company} />
            {#if $errors.company}
              <p class="text-sm text-red-500">{$errors.company}</p>
            {/if}
          </div>

          <!-- Position -->
          <div class="space-y-2">
            <Label for="position">Position *</Label>
            <MultiCombobox
              options={occupationOptions}
              selectedValues={selectedPosition}
              placeholder="Search for positions..."
              searchPlaceholder="Search occupations..."
              emptyMessage="No positions found"
              width="w-full"
              maxDisplayItems={1}
              searchOptions={searchOccupations}
              onSelectedValuesChange={handlePositionChange} />
            <input type="hidden" name="position" bind:value={$form.position} />
            {#if $errors.position}
              <p class="text-sm text-red-500">{$errors.position}</p>
            {/if}
          </div>

          <!-- Location -->
          <div class="space-y-2">
            <Label for="location">Location</Label>
            <MultiCombobox
              options={locationOptions}
              selectedValues={selectedLocation}
              placeholder="Search for cities..."
              searchPlaceholder="Search locations..."
              emptyMessage="No locations found"
              width="w-full"
              maxDisplayItems={1}
              searchOptions={searchLocations}
              onSelectedValuesChange={handleLocationChange} />
            <input type="hidden" name="location" bind:value={$form.location} />
            {#if $errors.location}
              <p class="text-sm text-red-500">{$errors.location}</p>
            {/if}
          </div>

          <!-- Job Type -->
          <div class="space-y-2">
            <Label for="jobType">Job Type *</Label>
            <Select.Root
              type="single"
              value={$form.jobType}
              onValueChange={(value) => {
                $form.jobType = value || '';
              }}>
              <Select.Trigger id="jobType" class="w-full">
                <Select.Value placeholder="Select job type" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each jobTypes as type}
                    <Select.Item value={type}>{type}</Select.Item>
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="jobType" bind:value={$form.jobType} />
            {#if $errors.jobType}
              <p class="text-sm text-red-500">{$errors.jobType}</p>
            {/if}
          </div>

          <!-- Applied Date -->
          <div class="space-y-2">
            <Label for="appliedDate">Applied Date *</Label>
            <Input
              id="appliedDate"
              name="appliedDate"
              type="date"
              bind:value={$form.appliedDate}
              {...$constraints.appliedDate} />
            {#if $errors.appliedDate}
              <p class="text-sm text-red-500">{$errors.appliedDate}</p>
            {/if}
          </div>

          <!-- Status -->
          <div class="space-y-2">
            <Label for="status">Status *</Label>
            <Select.Root
              type="single"
              value={$form.status}
              onValueChange={(value) => {
                $form.status = value || '';
              }}>
              <Select.Trigger id="status" class="w-full">
                <Select.Value placeholder="Select status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each jobStatuses as status}
                    <Select.Item value={status}>{status}</Select.Item>
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="status" bind:value={$form.status} />
            {#if $errors.status}
              <p class="text-sm text-red-500">{$errors.status}</p>
            {/if}
          </div>

          <!-- Resume -->
          <div class="space-y-2">
            <Label for="resumeUploaded">Resume *</Label>
            <Select.Root
              type="single"
              value={$form.resumeUploaded}
              onValueChange={(value) => {
                $form.resumeUploaded = value || '';
                selectedDocument = value || '';
              }}>
              <Select.Trigger id="resumeUploaded" class="w-full">
                <Select.Value placeholder="Select resume or status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Group>
                  {#each documentOptions as option}
                    {#if option.value === 'separator'}
                      <Select.Separator />
                      <Select.Label class="text-muted-foreground px-2 py-1 text-xs"
                        >{option.label}</Select.Label>
                    {:else}
                      <Select.Item value={option.value}>{option.label}</Select.Item>
                    {/if}
                  {/each}
                </Select.Group>
              </Select.Content>
            </Select.Root>
            <input type="hidden" name="resumeUploaded" bind:value={$form.resumeUploaded} />
            {#if $errors.resumeUploaded}
              <p class="text-sm text-red-500">{$errors.resumeUploaded}</p>
            {/if}
          </div>

          <!-- URL -->
          <div class="space-y-2">
            <Label for="url">Job URL</Label>
            <Input
              id="url"
              name="url"
              bind:value={$form.url}
              placeholder="https://example.com/job/123"
              {...$constraints.url} />
            {#if $errors.url}
              <p class="text-sm text-red-500">{$errors.url}</p>
            {/if}
          </div>

          <!-- Next Action -->
          <div class="space-y-2">
            <Label for="nextAction">Next Action</Label>
            <Input
              id="nextAction"
              name="nextAction"
              bind:value={$form.nextAction}
              placeholder="Follow up with recruiter"
              {...$constraints.nextAction} />
            {#if $errors.nextAction}
              <p class="text-sm text-red-500">{$errors.nextAction}</p>
            {/if}
          </div>

          <!-- Notes - Full width -->
          <div class="space-y-2 md:col-span-2">
            <Label for="notes">Notes</Label>
            <Textarea
              id="notes"
              name="notes"
              bind:value={$form.notes}
              placeholder="Any additional notes about this application"
              {...$constraints.notes} />
            {#if $errors.notes}
              <p class="text-sm text-red-500">{$errors.notes}</p>
            {/if}
          </div>
        </div>

        <div class="mt-4 flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onclick={() => {
              reset();
              open = false;
            }}>Cancel</Button>
          <Button
            type="submit"
            disabled={submitting}
            class="bg-primary text-primary-foreground hover:bg-primary/90">
            {#if submitting}
              <Loader2 class="mr-2 h-4 w-4 animate-spin" />
              Saving...
            {:else}
              <Plus class="mr-2 h-4 w-4" />
              Add Application
            {/if}
          </Button>
        </div>
      </form>
    </div></Dialog.Content>
</Dialog.Root>
